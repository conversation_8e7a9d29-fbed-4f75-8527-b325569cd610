import 'dart:async';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../database/local_database_manager.dart';
import '../utils/logger.dart';
import '../router/app_router.dart';
import '../../constants/app_constants.dart';
import '../../features/activity/domain/services/activity_service.dart';
import '../../features/activity/data/repositories/activity_repository_impl.dart';
import 'package:firebase_auth/firebase_auth.dart';

/// 🔔 推播通知服務
class NotificationService {
  static final NotificationService _instance = NotificationService._internal();
  factory NotificationService() => _instance;
  NotificationService._internal();

  // 全局導航上下文
  static BuildContext? _globalContext;

  /// 設置全局導航上下文
  static void setGlobalContext(BuildContext context) {
    _globalContext = context;
    Logger.i('🌐 Global context set for NotificationService');
    debugPrint(
        '🌐 [NotificationService] Global context set: ${context.runtimeType}');
  }

  /// 公共方法：處理通知點擊
  void handleNotificationTap(Map<String, dynamic> data) {
    debugPrint('🔔 [NotificationService] handleNotificationTap called');
    debugPrint('🔔 [NotificationService] Notification data: $data');
    debugPrint('🔔 [NotificationService] Data keys: ${data.keys.toList()}');
    debugPrint(
        '🔔 [NotificationService] Global context available: ${_globalContext != null}');
    _handleNotificationTap(data);
  }

  // 本地通知插件
  final FlutterLocalNotificationsPlugin _localNotifications =
      FlutterLocalNotificationsPlugin();

  // Firebase 訊息
  final FirebaseMessaging _firebaseMessaging = FirebaseMessaging.instance;

  // 通知通道
  static const AndroidNotificationChannel _channel = AndroidNotificationChannel(
    'high_importance_channel',
    '重要通知',
    description: '自言自語的重要通知',
    importance: Importance.high,
  );

  // 通知設定
  static const AndroidNotificationDetails _androidSettings =
      AndroidNotificationDetails(
    'high_importance_channel',
    '重要通知',
    channelDescription: '自言自語的重要通知',
    importance: Importance.high,
    priority: Priority.high,
    showWhen: true,
    enableVibration: true,
    enableLights: true,
    color: Color(0xFF00ACC1),
    icon: '@mipmap/ic_launcher',
  );

  static const DarwinNotificationDetails _iosSettings =
      DarwinNotificationDetails(
    presentAlert: true,
    presentBadge: true,
    presentSound: true,
  );

  static const NotificationDetails _notificationDetails = NotificationDetails(
    android: _androidSettings,
    iOS: _iosSettings,
  );

  // 初始化狀態
  bool _isInitialized = false;
  String? _fcmToken;
  StreamSubscription<RemoteMessage>? _foregroundSubscription;
  StreamSubscription<RemoteMessage>? _backgroundSubscription;

  /// 初始化通知服務
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // 初始化本地通知
      await _initializeLocalNotifications();

      // 初始化 Firebase 訊息
      await _initializeFirebaseMessaging();

      // 設定前台訊息處理
      _setupForegroundMessageHandler();

      // 設定背景訊息處理
      _setupBackgroundMessageHandler();

      _isInitialized = true;
      debugPrint('🔔 通知服務初始化完成');
    } catch (error) {
      debugPrint('❌ 通知服務初始化失敗: $error');
      rethrow;
    }
  }

  /// 初始化本地通知
  Future<void> _initializeLocalNotifications() async {
    const AndroidInitializationSettings initializationSettingsAndroid =
        AndroidInitializationSettings('@mipmap/ic_launcher');

    const DarwinInitializationSettings initializationSettingsIOS =
        DarwinInitializationSettings(
      requestAlertPermission: true,
      requestBadgePermission: true,
      requestSoundPermission: true,
    );

    const InitializationSettings initializationSettings =
        InitializationSettings(
      android: initializationSettingsAndroid,
      iOS: initializationSettingsIOS,
    );

    await _localNotifications.initialize(
      initializationSettings,
      onDidReceiveNotificationResponse: _onNotificationTapped,
    );

    // 創建通知通道 (Android)
    await _localNotifications
        .resolvePlatformSpecificImplementation<
            AndroidFlutterLocalNotificationsPlugin>()
        ?.createNotificationChannel(_channel);
  }

  /// 初始化 Firebase 訊息
  Future<void> _initializeFirebaseMessaging() async {
    // 請求權限
    final settings = await _firebaseMessaging.requestPermission(
      alert: true,
      announcement: false,
      badge: true,
      carPlay: false,
      criticalAlert: false,
      provisional: false,
      sound: true,
    );

    if (settings.authorizationStatus == AuthorizationStatus.authorized) {
      debugPrint('🔔 通知權限已授權');
    } else {
      debugPrint('❌ 通知權限被拒絕');
    }

    // 獲取 FCM Token
    _fcmToken = await _firebaseMessaging.getToken();
    debugPrint('🔔 FCM Token: $_fcmToken');

    // 監聽 Token 更新
    _firebaseMessaging.onTokenRefresh.listen((token) {
      _fcmToken = token;
      _saveFcmToken(token);
      debugPrint('🔔 FCM Token 已更新: $token');
    });
  }

  /// 設定前台訊息處理
  void _setupForegroundMessageHandler() {
    _foregroundSubscription = FirebaseMessaging.onMessage.listen((message) {
      debugPrint('🔔 收到前台訊息: ${message.messageId}');
      _handleForegroundMessage(message);
    });
  }

  /// 設定背景訊息處理
  void _setupBackgroundMessageHandler() {
    FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);
  }

  /// 處理前台訊息
  Future<void> _handleForegroundMessage(RemoteMessage message) async {
    final notification = message.notification;
    final data = message.data;

    if (notification != null) {
      // 顯示本地通知
      await _localNotifications.show(
        notification.hashCode,
        notification.title,
        notification.body,
        _notificationDetails,
        payload: json.encode(data),
      );

      // 儲存通知記錄
      await _saveNotificationRecord(
        notificationId: message.messageId ?? DateTime.now().toString(),
        type: data['type'] ?? 'general',
        data: data,
      );

      // 將通知轉換為活動記錄
      await _convertNotificationToActivity(
        notificationId: message.messageId ?? DateTime.now().toString(),
        type: data['type'] ?? 'general',
        data: data,
      );
    }
  }

  /// 處理通知點擊
  void _onNotificationTapped(NotificationResponse response) {
    final payload = response.payload;
    if (payload != null) {
      final data = json.decode(payload) as Map<String, dynamic>;
      _handleNotificationTap(data);
    }
  }

  /// 處理通知點擊事件
  void _handleNotificationTap(Map<String, dynamic> data) {
    debugPrint('🔔 [NotificationService] _handleNotificationTap started');
    debugPrint('🔔 [NotificationService] Raw data: $data');

    final type = data['type'] as String?;
    final postId = data['post_id'] as String? ?? data['postId'] as String?;
    final userId = data['user_id'] as String? ?? data['userId'] as String?;
    final followerId = data['follower_id'] as String?;
    final likerId = data['liker_id'] as String?;
    final commenterId = data['commenter_id'] as String?;

    debugPrint('🔔 [NotificationService] Parsed values:');
    debugPrint('   - type: $type');
    debugPrint('   - postId: $postId');
    debugPrint('   - userId: $userId');
    debugPrint('   - followerId: $followerId');
    debugPrint('   - likerId: $likerId');
    debugPrint('   - commenterId: $commenterId');

    Logger.i(
        '🔔 Handling notification tap: type=$type, postId=$postId, userId=$userId');

    switch (type) {
      case 'post_like':
      case 'like':
        if (postId != null) {
          _navigateToPost(postId);
        }
        break;
      case 'post_comment':
      case 'comment':
        if (postId != null) {
          _navigateToPost(postId);
        }
        break;
      case 'comment_reply':
      case 'reply':
        if (postId != null) {
          _navigateToPost(postId);
        }
        break;
      case 'follow':
        final targetUserId = followerId ?? userId;
        if (targetUserId != null) {
          _navigateToUserProfile(targetUserId);
        }
        break;
      case 'followed_user_post':
        if (postId != null) {
          _navigateToPost(postId);
        }
        break;
      case 'followed_user_comment':
        if (postId != null) {
          _navigateToPost(postId);
        }
        break;
      case 'followed_geo_tag_post':
        final geoTagId = data['geo_tag_id'] as String?;
        final geoTagName = data['geoTagName'] as String?;
        if (geoTagName != null) {
          _navigateToGeoTagPosts(geoTagName);
        } else if (postId != null) {
          // 備用方案：直接跳轉到文章
          _navigateToPost(postId);
        }
        break;
      case 'bookmark':
      case 'post_bookmark':
        if (postId != null) {
          _navigateToPost(postId);
        }
        break;
      case 'broadcast':
        // 導航到通知頁面
        _navigateToNotifications();
        break;
      default:
        Logger.w('⚠️ Unknown notification type: $type');
        _navigateToNotifications();
        break;
    }
  }

  /// 導航到貼文詳情
  void _navigateToPost(String postId) {
    debugPrint(
        '🔔 [NotificationService] _navigateToPost called with postId: $postId');

    Logger.i('🔔 導航到貼文: $postId');

    // 使用更可靠的方式等待應用完全初始化
    _waitForAppInitialization().then((_) {
      try {
        debugPrint(
            '🔔 [NotificationService] Attempting to navigate to: /post/$postId');
        // 使用全局 GoRouter 實例進行導航
        globalRouter.push('/post/$postId');
        debugPrint('🔔 [NotificationService] Navigation successful');
      } catch (error) {
        debugPrint('🔔 [NotificationService] Navigation failed: $error');
        Logger.e('❌ 導航到貼文失敗: $error');
        // 備用方案：導航到首頁
        debugPrint('🔔 [NotificationService] Falling back to home page');
        try {
          globalRouter.go('/home');
        } catch (fallbackError) {
          debugPrint(
              '🔔 [NotificationService] Fallback navigation also failed: $fallbackError');
          Logger.e('❌ 備用導航也失敗: $fallbackError');
        }
      }
    });
  }

  /// 將通知轉換為活動記錄
  Future<void> _convertNotificationToActivity({
    required String notificationId,
    required String type,
    required Map<String, dynamic> data,
  }) async {
    try {
      final currentUser = FirebaseAuth.instance.currentUser;
      if (currentUser == null) return;

      final activityService = ActivityService(ActivityRepositoryImpl());

      await activityService.convertNotificationToActivity(
        notificationId: notificationId,
        type: type,
        targetUserId: currentUser.uid,
        data: data,
        receivedAt: DateTime.now(),
      );

      debugPrint('✅ 通知已轉換為活動記錄: $type');
    } catch (error) {
      debugPrint('❌ 轉換通知為活動記錄失敗: $error');
      // 不要因為活動記錄失敗而影響通知功能
    }
  }

  /// 等待應用完全初始化
  Future<void> _waitForAppInitialization() async {
    // 等待 Widget 樹完全構建
    await WidgetsBinding.instance.endOfFrame;

    // 額外等待確保路由器準備就緒
    int attempts = 0;
    const maxAttempts = 10;

    while (attempts < maxAttempts) {
      try {
        // 嘗試獲取當前路由狀態
        final currentLocation =
            globalRouter.routerDelegate.currentConfiguration;
        debugPrint('🔔 [NotificationService] App initialization confirmed');
        return;
      } catch (e) {
        debugPrint(
            '🔔 [NotificationService] Waiting for router initialization: attempt ${attempts + 1}');
      }

      await Future.delayed(const Duration(milliseconds: 200));
      attempts++;
    }

    debugPrint(
        '🔔 [NotificationService] Max attempts reached, proceeding with navigation');
  }

  /// 導航到用戶檔案
  void _navigateToUserProfile(String userId) {
    debugPrint(
        '🔔 [NotificationService] _navigateToUserProfile called with userId: $userId');

    Logger.i('🔔 導航到用戶檔案: $userId');
    try {
      debugPrint(
          '🔔 [NotificationService] Attempting to navigate to: /user/$userId');
      // 使用全局 GoRouter 實例進行導航
      globalRouter.push('/user/$userId');
      debugPrint('🔔 [NotificationService] User profile navigation successful');
    } catch (error) {
      debugPrint(
          '🔔 [NotificationService] User profile navigation failed: $error');
      Logger.e('❌ 導航到用戶檔案失敗: $error');
      // 備用方案：導航到首頁
      debugPrint('🔔 [NotificationService] Falling back to home page');
      try {
        globalRouter.go('/home');
      } catch (fallbackError) {
        debugPrint(
            '🔔 [NotificationService] Fallback navigation also failed: $fallbackError');
        Logger.e('❌ 備用導航也失敗: $fallbackError');
      }
    }
  }

  /// 導航到地理標籤文章列表
  void _navigateToGeoTagPosts(String geoTagName) {
    debugPrint(
        '🔔 [NotificationService] _navigateToGeoTagPosts called with geoTagName: $geoTagName');

    Logger.i('🔔 導航到地理標籤文章列表: $geoTagName');

    // 使用更可靠的方式等待應用完全初始化
    _waitForAppInitialization().then((_) {
      try {
        final url =
            '/posts?filterType=geoTag&filterValue=${Uri.encodeComponent(geoTagName)}&title=${Uri.encodeComponent(geoTagName)}';
        debugPrint('🔔 [NotificationService] Attempting to navigate to: $url');
        // 使用全局 GoRouter 實例進行導航
        globalRouter.push(url);
        debugPrint(
            '🔔 [NotificationService] GeoTag posts navigation successful');
      } catch (error) {
        debugPrint(
            '🔔 [NotificationService] GeoTag posts navigation failed: $error');
        Logger.e('❌ 導航到地理標籤文章列表失敗: $error');
        // 備用方案：導航到首頁
        debugPrint('🔔 [NotificationService] Falling back to home page');
        try {
          globalRouter.go('/home');
        } catch (fallbackError) {
          debugPrint(
              '🔔 [NotificationService] Fallback navigation also failed: $fallbackError');
          Logger.e('❌ 備用導航也失敗: $fallbackError');
        }
      }
    });
  }

  /// 導航到通知頁面
  void _navigateToNotifications() {
    debugPrint('🔔 [NotificationService] _navigateToNotifications called');

    Logger.i('🔔 導航到通知頁面');
    try {
      debugPrint(
          '🔔 [NotificationService] Attempting to navigate to: /notifications');
      // 使用全局 GoRouter 實例進行導航
      globalRouter.push('/notifications');
      debugPrint(
          '🔔 [NotificationService] Notifications navigation successful');
    } catch (error) {
      debugPrint(
          '🔔 [NotificationService] Notifications navigation failed: $error');
      Logger.e('❌ 導航到通知頁面失敗: $error');
      // 備用方案：導航到設定頁面
      debugPrint('🔔 [NotificationService] Falling back to settings page');
      try {
        globalRouter.go('/settings');
      } catch (fallbackError) {
        debugPrint(
            '🔔 [NotificationService] Fallback navigation also failed: $fallbackError');
        Logger.e('❌ 備用導航也失敗: $fallbackError');
      }
    }
  }

  /// 儲存 FCM Token
  Future<void> _saveFcmToken(String token) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(AppConstants.fcmTokenKey, token);
  }

  /// 儲存通知記錄
  Future<void> _saveNotificationRecord({
    required String notificationId,
    required String type,
    required Map<String, dynamic> data,
  }) async {
    final localDB = LocalDatabaseManager();
    await localDB.recordNotification(
      NotificationRecord(
        notificationId: notificationId,
        type: type,
        data: data,
      ),
    );
  }

  /// 發送本地通知
  Future<void> showLocalNotification({
    required String title,
    required String body,
    String? payload,
    int? id,
  }) async {
    // 確保 ID 在 32 位整數範圍內
    final notificationId =
        id ?? (DateTime.now().millisecondsSinceEpoch % 2147483647);

    await _localNotifications.show(
      notificationId,
      title,
      body,
      _notificationDetails,
      payload: payload,
    );
  }

  /// 取消通知
  Future<void> cancelNotification(int id) async {
    await _localNotifications.cancel(id);
  }

  /// 取消所有通知
  Future<void> cancelAllNotifications() async {
    await _localNotifications.cancelAll();
  }

  /// 獲取 FCM Token
  String? get fcmToken => _fcmToken;

  /// 檢查通知權限
  Future<bool> checkNotificationPermission() async {
    final settings = await _firebaseMessaging.getNotificationSettings();
    return settings.authorizationStatus == AuthorizationStatus.authorized;
  }

  /// 請求通知權限
  Future<bool> requestNotificationPermission() async {
    final settings = await _firebaseMessaging.requestPermission();
    return settings.authorizationStatus == AuthorizationStatus.authorized;
  }

  /// 訂閱主題
  Future<void> subscribeToTopic(String topic) async {
    await _firebaseMessaging.subscribeToTopic(topic);
    debugPrint('🔔 已訂閱主題: $topic');
  }

  /// 取消訂閱主題
  Future<void> unsubscribeFromTopic(String topic) async {
    await _firebaseMessaging.unsubscribeFromTopic(topic);
    debugPrint('🔔 已取消訂閱主題: $topic');
  }

  /// 清理資源
  void dispose() {
    _foregroundSubscription?.cancel();
    _backgroundSubscription?.cancel();
  }
}

/// 🔔 背景訊息處理器
@pragma('vm:entry-point')
Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  debugPrint('🔔 收到背景訊息: ${message.messageId}');

  // 初始化 Firebase
  // await Firebase.initializeApp();

  // 處理背景訊息
  final data = message.data;
  final notification = message.notification;

  if (notification != null) {
    // 顯示本地通知
    final localNotifications = FlutterLocalNotificationsPlugin();
    await localNotifications.show(
      notification.hashCode,
      notification.title,
      notification.body,
      const NotificationDetails(
        android: AndroidNotificationDetails(
          'high_importance_channel',
          '重要通知',
          channelDescription: '自言自語的重要通知',
          importance: Importance.high,
          priority: Priority.high,
        ),
      ),
    );
  }
}

/// 🔔 通知服務 Provider
final notificationServiceProvider = Provider<NotificationService>((ref) {
  return NotificationService();
});

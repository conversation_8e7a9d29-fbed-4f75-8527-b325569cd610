// 文章互動 provider - 處理點讚、收藏、分享等操作
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../data/services/user_favorite_service.dart';
import '../../data/services/user_follow_service.dart';
import '../../../favorite/presentation/providers/favorite_provider.dart';
import '../../../notifications/data/services/notification_trigger_service.dart';

// 狀態類 - 包含所有互動狀態
class PostInteractionState {
  final Set<String> likedPosts;
  final Set<String> likedComments;
  final Set<String> favoritedPosts; // 改名為 favoritedPosts
  final Set<String> followingUsers;
  final Map<String, int> commentLikeCounts;
  final Set<String> viewedPosts; // 新增已查看的文章

  const PostInteractionState({
    this.likedPosts = const {},
    this.likedComments = const {},
    this.favoritedPosts = const {}, // 改名
    this.followingUsers = const {},
    this.commentLikeCounts = const {},
    this.viewedPosts = const {}, // 新增
  });

  PostInteractionState copyWith({
    Set<String>? likedPosts,
    Set<String>? likedComments,
    Set<String>? favoritedPosts, // 改名
    Set<String>? followingUsers,
    Map<String, int>? commentLikeCounts,
    Set<String>? viewedPosts, // 新增
  }) {
    return PostInteractionState(
      likedPosts: likedPosts ?? this.likedPosts,
      likedComments: likedComments ?? this.likedComments,
      favoritedPosts: favoritedPosts ?? this.favoritedPosts, // 改名
      followingUsers: followingUsers ?? this.followingUsers,
      commentLikeCounts: commentLikeCounts ?? this.commentLikeCounts,
      viewedPosts: viewedPosts ?? this.viewedPosts, // 新增
    );
  }
}

// 文章互動通知器
class PostInteractionNotifier extends StateNotifier<PostInteractionState> {
  PostInteractionNotifier(this._ref) : super(const PostInteractionState()) {
    _initialize();
  }

  final Ref _ref;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;
  late final UserFavoriteService _favoriteService;
  late final UserFollowService _followService;
  late final SharedPreferences _prefs;

  Future<void> _initialize() async {
    _favoriteService = await UserFavoriteService.createWithCache();
    _followService = await UserFollowService.createWithCache();
    _prefs = await SharedPreferences.getInstance();
    await _loadUserData(); // 等待載入完成
    _setupAuthListener();
  }

  // 設置認證監聽器
  void _setupAuthListener() {
    _auth.authStateChanges().listen((user) {
      if (user != null) {
        _loadUserData();
      } else {
        // 用戶登出時清空狀態
        state = const PostInteractionState();
      }
    });
  }

  // 載入用戶資料（點讚、收藏等）
  Future<void> _loadUserData() async {
    await _loadUserLikes();
    await _loadUserFavorites();
    await _loadViewedPosts();
    await _loadUserFollowing();
  }

  // 載入用戶已點讚的文章
  Future<void> _loadUserLikes() async {
    final currentUser = _auth.currentUser;
    if (currentUser == null) return;

    try {
      final likesSnapshot = await _firestore
          .collection('likes')
          .where('user_id', isEqualTo: currentUser.uid)
          .get();

      final likedPosts = likesSnapshot.docs
          .map((doc) => doc.data()['post_id'] as String?)
          .where((id) => id != null)
          .cast<String>()
          .toSet();

      state = state.copyWith(likedPosts: likedPosts);
    } catch (e) {
      // 忽略載入錯誤
    }
  }

  // 點讚文章
  Future<void> likePost(String postId, [dynamic context]) async {
    final currentUser = _auth.currentUser;
    if (currentUser == null) return;

    try {
      // 更新本地狀態
      state = state.copyWith(
        likedPosts: {...state.likedPosts, postId},
      );

      // 使用 transaction 更新 Firestore
      await _firestore.runTransaction((transaction) async {
        final postRef = _firestore.collection('posts').doc(postId);
        final postSnapshot = await transaction.get(postRef);

        if (postSnapshot.exists) {
          final data = postSnapshot.data() as Map<String, dynamic>?;
          final currentLikes = data?['stats']?['likeCount'] ?? 0;

          // 使用 dot notation 更新特定欄位
          transaction.update(postRef, {
            'stats.likeCount': currentLikes + 1,
          });
        }
      });

      // 記錄點讚到 likes 集合
      await _firestore
          .collection('likes')
          .doc('${postId}_${currentUser.uid}')
          .set({
        'post_id': postId,
        'user_id': currentUser.uid,
        'created_at': FieldValue.serverTimestamp(),
      });

      // 發送點讚通知
      try {
        await NotificationTriggerService.sendLikeNotification(
          postId: postId,
          likerId: currentUser.uid,
          isAnonymous: false,
        );
      } catch (notificationError) {
        print('⚠️ Failed to send like notification: $notificationError');
        // 不要因為通知失敗而影響點讚功能
      }
    } catch (e) {
      // 還原本地狀態
      state = state.copyWith(
        likedPosts: state.likedPosts.difference({postId}),
      );
      print('❌ Error liking post: $e');
      rethrow;
    }
  }

  // 取消點讚文章
  Future<void> unlikePost(String postId) async {
    final currentUser = _auth.currentUser;
    if (currentUser == null) return;

    try {
      // 更新本地狀態
      state = state.copyWith(
        likedPosts: state.likedPosts.difference({postId}),
      );

      // 使用 transaction 更新 Firestore
      await _firestore.runTransaction((transaction) async {
        final postRef = _firestore.collection('posts').doc(postId);
        final postSnapshot = await transaction.get(postRef);

        if (postSnapshot.exists) {
          final data = postSnapshot.data() as Map<String, dynamic>?;
          final currentLikes = data?['stats']?['likeCount'] ?? 0;

          // 使用 dot notation 更新特定欄位
          transaction.update(postRef, {
            'stats.likeCount': currentLikes > 0 ? currentLikes - 1 : 0,
          });
        }
      });

      // 刪除點讚記錄
      await _firestore
          .collection('likes')
          .doc('${postId}_${currentUser.uid}')
          .delete();
    } catch (e) {
      // 還原本地狀態
      state = state.copyWith(
        likedPosts: {...state.likedPosts, postId},
      );
      print('❌ Error unliking post: $e');
      rethrow;
    }
  }

  // 載入用戶已收藏的文章
  Future<void> _loadUserFavorites() async {
    final currentUser = _auth.currentUser;
    if (currentUser == null) return;

    try {
      await _favoriteService.initializeUserCache(currentUser.uid);
      final favoritedPosts = await _favoriteService.getFavoritedPosts();

      state = state.copyWith(favoritedPosts: favoritedPosts.toSet());
    } catch (e) {
      // 忽略載入錯誤
    }
  }

  // 載入已查看的文章（從本地儲存）
  Future<void> _loadViewedPosts() async {
    final currentUser = _auth.currentUser;
    if (currentUser == null) return;

    try {
      // 確保 SharedPreferences 已初始化
      if (_prefs == null) {
        print(
            '⚠️ SharedPreferences not initialized, skipping viewedPosts load');
        return;
      }

      final key = 'viewed_posts_${currentUser.uid}';
      final viewedPosts = _prefs.getStringList(key) ?? [];
      state = state.copyWith(viewedPosts: viewedPosts.toSet());
      print(
          '✅ Loaded ${viewedPosts.length} viewed posts from SharedPreferences');
    } catch (e) {
      print('❌ Error loading viewed posts: $e');
      // 忽略載入錯誤
    }
  }

  // 收藏文章
  Future<void> favoritePost(String postId) async {
    final currentUser = _auth.currentUser;
    if (currentUser == null) return;

    try {
      // 更新本地狀態
      state = state.copyWith(
        favoritedPosts: {...state.favoritedPosts, postId},
      );

      // 呼叫服務處理 Firestore 更新
      await _favoriteService.favoritePost(postId);

      // 發送收藏通知
      try {
        await NotificationTriggerService.sendBookmarkNotification(
          postId: postId,
          bookmarkerId: currentUser.uid,
        );
      } catch (notificationError) {
        print('⚠️ Failed to send bookmark notification: $notificationError');
        // 不要因為通知失敗而影響收藏功能
      }

      // 通知 favoriteProvider 重新載入
      try {
        _ref.read(favoriteProvider.notifier).refreshFavorites(currentUser.uid);
      } catch (e) {
        print('⚠️ Failed to refresh favorite provider: $e');
      }
    } catch (e) {
      // 回滾本地狀態
      state = state.copyWith(
        favoritedPosts:
            state.favoritedPosts.where((id) => id != postId).toSet(),
      );
      rethrow;
    }
  }

  // 取消收藏文章
  Future<void> unfavoritePost(String postId) async {
    final currentUser = _auth.currentUser;
    if (currentUser == null) return;

    try {
      // 更新本地狀態
      state = state.copyWith(
        favoritedPosts:
            state.favoritedPosts.where((id) => id != postId).toSet(),
      );

      // 呼叫服務處理 Firestore 更新
      await _favoriteService.unfavoritePost(postId);

      // 通知 favoriteProvider 重新載入
      try {
        _ref.read(favoriteProvider.notifier).refreshFavorites(currentUser.uid);
      } catch (e) {
        print('⚠️ Failed to refresh favorite provider: $e');
      }
    } catch (e) {
      // 回滾本地狀態
      state = state.copyWith(
        favoritedPosts: {...state.favoritedPosts, postId},
      );
      rethrow;
    }
  }

  // 檢查文章是否已收藏
  bool isPostFavorited(String postId) {
    return state.favoritedPosts.contains(postId);
  }

  // 刷新點讚狀態
  Future<void> refreshLikeStatus() async {
    await _loadUserLikes();
  }

  // 刷新所有快取
  Future<void> refreshCacheForCurrentAccount() async {
    await _loadUserData();
  }

  // 為了向後兼容，保留 bookmarkPost 方法但呼叫 favoritePost
  Future<void> bookmarkPost(String postId) async => await favoritePost(postId);
  Future<void> unbookmarkPost(String postId) async =>
      await unfavoritePost(postId);

  // 記錄文章查看（每個用戶每篇文章只記錄一次）
  @Deprecated('使用 HomeRepository.viewPost() 替代，避免重複的 viewCount 追蹤邏輯')
  Future<void> trackPostView(String postId) async {
    // 此方法已棄用，請使用 HomeRepository.viewPost() 來統一處理 viewCount
    debugPrint('⚠️ trackPostView() 已棄用，請使用 HomeRepository.viewPost()');

    final currentUser = _auth.currentUser;
    if (currentUser == null) return;

    // 只更新本地狀態，不更新 Firestore（避免重複計算）
    if (!state.viewedPosts.contains(postId)) {
      state = state.copyWith(
        viewedPosts: {...state.viewedPosts, postId},
      );

      // 保存到本地儲存
      try {
        final key = 'viewed_posts_${currentUser.uid}';
        await _prefs.setStringList(key, state.viewedPosts.toList());
      } catch (e) {
        debugPrint('⚠️ SharedPreferences error: $e');
      }
    }
  }

  // 檢查文章是否已查看
  bool isPostViewed(String postId) {
    return state.viewedPosts.contains(postId);
  }

  // 清除查看記錄（用於測試或用戶登出）
  Future<void> clearViewHistory() async {
    final currentUser = _auth.currentUser;
    if (currentUser == null) return;

    final key = 'viewed_posts_${currentUser.uid}';
    await _prefs.remove(key);
    state = state.copyWith(viewedPosts: {});
  }

  // 載入用戶追蹤列表
  Future<void> _loadUserFollowing() async {
    final currentUser = _auth.currentUser;
    if (currentUser == null) return;

    try {
      await _followService.initializeUserCache(currentUser.uid);
      final following = await _followService.getFollowing(currentUser.uid);
      state = state.copyWith(followingUsers: following.toSet());
      print('✅ Loaded ${following.length} following users: $following');
    } catch (e) {
      print('❌ Error loading following users: $e');
      // 忽略載入錯誤
    }
  }

  // 追蹤用戶
  Future<void> followUser(String userId) async {
    final currentUser = _auth.currentUser;
    if (currentUser == null) return;

    try {
      // 更新本地狀態
      state = state.copyWith(
        followingUsers: {...state.followingUsers, userId},
      );

      // 呼叫服務處理 Firestore 更新（包含通知）
      await _followService.followUser(currentUser.uid, userId);
    } catch (e) {
      // 回滾本地狀態
      state = state.copyWith(
        followingUsers:
            state.followingUsers.where((id) => id != userId).toSet(),
      );
      print('❌ Error following user: $e');
      rethrow;
    }
  }

  // 取消追蹤用戶
  Future<void> unfollowUser(String userId) async {
    final currentUser = _auth.currentUser;
    if (currentUser == null) return;

    try {
      // 更新本地狀態
      state = state.copyWith(
        followingUsers:
            state.followingUsers.where((id) => id != userId).toSet(),
      );

      // 呼叫服務處理 Firestore 更新
      await _followService.unfollowUser(currentUser.uid, userId);
    } catch (e) {
      // 回滾本地狀態
      state = state.copyWith(
        followingUsers: {...state.followingUsers, userId},
      );
      print('❌ Error unfollowing user: $e');
      rethrow;
    }
  }

  // 檢查是否正在追蹤用戶
  bool isFollowingUser(String userId) {
    return state.followingUsers.contains(userId);
  }

  // 其他方法暫時保持空實現
  Future<void> togglePostLike(String postId) async {}
  Future<void> toggleCommentLike(String commentId) async {}
  Future<void> toggleBookmark(String postId) async {}
  Future<void> sharePost(String postId) async {}
}

// 暫時的 provider
final postInteractionProvider =
    StateNotifierProvider<PostInteractionNotifier, PostInteractionState>((ref) {
  return PostInteractionNotifier(ref);
});

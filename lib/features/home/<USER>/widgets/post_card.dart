import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:firebase_auth/firebase_auth.dart';

import '../../../../shared/widgets/app_widgets.dart';
import '../../../../shared/widgets/blocked_content_overlay.dart';
import '../../../../shared/animations/animation_manager_mixin.dart';
import '../../../../core/auth/auth_guard.dart';
import '../../../../core/theme/accessible_colors.dart';
import '../../../../core/services/block_service.dart';
import '../../../../core/utils/logger.dart';
import '../../../admin/domain/services/admin_permission_service.dart';
import '../../domain/entities/post.dart';
import '../../domain/entities/report.dart';
import '../../data/repositories/home_repository_impl.dart';
import '../providers/home_provider.dart';
import '../providers/crud_provider.dart';
import '../providers/post_interaction_provider.dart';
import '../../../auth/presentation/providers/auth_provider.dart';
import 'post_components/post_header.dart';
import 'post_components/post_content.dart';
import 'post_components/post_media.dart';
import 'post_components/post_actions.dart';
import 'post_components/post_tags.dart';
import 'report_dialog.dart';
import 'delete_confirmation_dialog.dart';

/// 📝 重構後的貼文卡片組件 - 使用模組化子組件
class PostCard extends ConsumerStatefulWidget {
  final Post post;
  final VoidCallback? onTap;
  final VoidCallback? onLongPress;
  final VoidCallback? onPostDeleted; // 新增：刪除後回調
  final bool showActions;
  final VoidCallback? onCommentTap;
  final bool useTransparentBackground;
  final bool enableInternalScrolling;

  const PostCard({
    super.key,
    required this.post,
    this.onTap,
    this.onLongPress,
    this.onPostDeleted, // 新增：刪除後回調
    this.showActions = true,
    this.onCommentTap,
    this.useTransparentBackground = false,
    this.enableInternalScrolling = true,
  });

  @override
  ConsumerState<PostCard> createState() => _PostCardState();
}

class _PostCardState extends ConsumerState<PostCard>
    with TickerProviderStateMixin, AnimationLifecycleMixin {
  @override
  void initState() {
    super.initState();

    // 設定進場動畫
    setupEntranceAnimations();
    registerAnimation(
      name: 'card_entrance',
      duration: const Duration(milliseconds: 300),
      autoStart: true,
    );

    // 記錄文章查看 - 使用統一的 HomeRepository 方法
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      if (mounted) {
        final auth = FirebaseAuth.instance;
        final currentUser = auth.currentUser;
        if (currentUser != null) {
          try {
            final homeRepository = HomeRepositoryImpl();
            await homeRepository.viewPost(widget.post.postId, currentUser.uid);
          } catch (e) {
            // 靜默處理錯誤，不影響 UI
            debugPrint('❌ Error tracking post view: $e');
          }
        }
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final blockedUsers = ref.watch(blockedUsersProvider);

    // 檢查作者是否被封鎖
    final authorIdentifier = widget.post.author.isAnonymous
        ? widget.post.author.userHash ?? widget.post.author.userId
        : widget.post.author.userId;
    final isBlocked = blockedUsers.any((user) => user.uid == authorIdentifier);

    // 建立卡片內容
    final cardChildren = [
      // 📄 貼文頭部：頭像、用戶名、時間、選項菜單
      PostHeader(
        post: widget.post,
        onMoreOptions: () => _showMoreOptions(context),
      ),

      // 📝 貼文內容：文字內容和展開/收縮功能
      PostContent(post: widget.post),

      // 🎬 媒體內容：圖片、視頻、GIF
      if (widget.post.imageUrls.isNotEmpty ||
          widget.post.videoUrls.isNotEmpty) ...[
        const SizedBox(height: 8),
        ConstrainedBox(
          constraints: const BoxConstraints(maxHeight: 200),
          child: PostMedia(
            post: widget.post,
            maxHeight: 200, // 嚴格限制媒體高度防止溢出
          ),
        ),
      ],

      // 🏷️ 標籤：Hashtags 和地理標籤
      if (widget.post.hashtags.isNotEmpty ||
          widget.post.location.geoTags.isNotEmpty) ...[
        const SizedBox(height: 12),
        PostTags(post: widget.post),
      ],

      // 🎯 操作按鈕：點讚、評論、分享、收藏
      if (widget.showActions)
        PostActions(
          post: widget.post,
          onCommentTap: widget.onCommentTap,
        ),
    ];

    const double borderRadius = 16.0; // 定義圓角半徑

    final cardContent = EntranceAnimation(
      delay: const Duration(milliseconds: 100),
      child: GestureDetector(
        onTap: widget.onTap ?? _navigateToPostDetail,
        onLongPress: widget.onLongPress,
        behavior: HitTestBehavior.opaque,
        child: Container(
          width: double.infinity,
          margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
          clipBehavior: Clip.hardEdge,
          decoration: BoxDecoration(
            color: widget.useTransparentBackground
                ? Colors.transparent
                : theme.colorScheme.surface,
            borderRadius: BorderRadius.circular(borderRadius),
            border: Border.all(
              color: theme.colorScheme.outline.withValues(alpha: 0.1),
              width: 1,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.04),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: LayoutBuilder(
            builder: (context, constraints) {
              // 如果沒有高度限制，使用Column
              if (constraints.maxHeight == double.infinity) {
                return Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: cardChildren,
                );
              }
              // 如果有高度限制，使用可滾動的SingleChildScrollView
              return SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: cardChildren,
                ),
              );
            },
          ),
        ),
      ),
    );

    // 如果用戶被封鎖，用覆蓋層包裝
    if (isBlocked) {
      return BlockedContentOverlay(
        blockedUserName: widget.post.author.displayName,
        child: cardContent,
        onTap: () {
          debugPrint(
              '🔍 PostCard: 暫時查看被封鎖用戶的內容 - ${widget.post.author.displayName}');
        },
      );
    }

    return cardContent;
  }

  /// 導航到貼文詳細頁面
  void _navigateToPostDetail() {
    debugPrint('🔍 PostCard: 導航到貼文詳細頁面 - ${widget.post.postId}');
    try {
      context.push('/post/${widget.post.postId}');
    } catch (e) {
      debugPrint('❌ PostCard: 導航失敗 - $e');
    }
  }

  /// 顯示更多選項菜單
  void _showMoreOptions(BuildContext context) async {
    final user = ref.read(authProvider).value;
    final isAuthor = user?.uid == widget.post.author.userId;
    final isAdmin =
        user != null && await AdminPermissionService.isCurrentUserAdmin();

    // 檢查是否正在追蹤這個用戶 (匿名用戶也可以被追蹤)
    bool isFollowing = false;
    if (user != null && !isAuthor) {
      try {
        // 🔥 對於匿名用戶，使用 userHash；對於一般用戶，使用 userId
        final targetId = widget.post.author.isAnonymous
            ? (widget.post.author.userHash ?? widget.post.author.userId)
            : widget.post.author.userId;

        isFollowing = await ref
            .read(postInteractionProvider.notifier)
            .isFollowingUser(targetId);
      } catch (e) {
        debugPrint('❌ 檢查追蹤狀態失敗: $e');
      }
    }

    if (!mounted) return;

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => _buildOptionsBottomSheet(
        context,
        isAuthor: isAuthor,
        isAdmin: isAdmin,
        isFollowing: isFollowing,
      ),
    );
  }

  /// 建立選項底部彈窗
  Widget _buildOptionsBottomSheet(
    BuildContext context, {
    required bool isAuthor,
    required bool isAdmin,
    required bool isFollowing,
  }) {
    final theme = Theme.of(context);

    return Container(
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(20),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 頂部把手
          Container(
            width: 40,
            height: 4,
            margin: const EdgeInsets.symmetric(vertical: 12),
            decoration: BoxDecoration(
              color: theme.colorScheme.outline.withValues(alpha: 0.4),
              borderRadius: BorderRadius.circular(2),
            ),
          ),

          // 選項列表
          if (isAuthor) ...[
            _buildOptionTile(
              context,
              icon: Icons.edit,
              title: '編輯貼文',
              onTap: () => _editPost(context),
            ),
            _buildOptionTile(
              context,
              icon: Icons.delete,
              title: '刪除貼文',
              onTap: () => _deletePost(context),
              isDestructive: true,
            ),
          ],

          if (!isAuthor) ...[
            // 🔥 追蹤/取消追蹤選項
            _buildOptionTile(
              context,
              icon: isFollowing ? Icons.person_remove : Icons.person_add,
              title: isFollowing ? '取消追蹤用戶' : '追蹤用戶',
              onTap: () async {
                // 先執行追蹤操作，避免導航問題
                if (isFollowing) {
                  await _performUnfollowUser();
                } else {
                  await _performFollowUser();
                }
                // 確保安全關閉底部彈窗，使用本地 navigator
                if (mounted &&
                    Navigator.of(context, rootNavigator: false).canPop()) {
                  Navigator.of(context, rootNavigator: false).pop();
                }
              },
              autoCloseSheet: false, // 手動控制關閉時機
            ),
            _buildOptionTile(
              context,
              icon: Icons.report,
              title: '檢舉貼文',
              onTap: () => _reportPost(context),
              isDestructive: true,
            ),
            _buildOptionTile(
              context,
              icon: Icons.block,
              title: '封鎖用戶',
              onTap: () => _blockUser(context),
              isDestructive: true,
            ),
          ],

          if (isAdmin)
            _buildOptionTile(
              context,
              icon: Icons.admin_panel_settings,
              title: '管理員操作',
              onTap: () => _showAdminOptions(context),
            ),

          const SizedBox(height: 16),
        ],
      ),
    );
  }

  /// 建立選項項目
  Widget _buildOptionTile(
    BuildContext context, {
    required IconData icon,
    required String title,
    required VoidCallback onTap,
    bool isDestructive = false,
    bool autoCloseSheet = true,
  }) {
    final theme = Theme.of(context);
    final color =
        isDestructive ? theme.colorScheme.error : theme.colorScheme.onSurface;

    return ListTile(
      leading: Icon(icon, color: color),
      title: Text(
        title,
        style: TextStyle(color: color),
      ),
      onTap: () {
        // 使用 Navigator.of(context, rootNavigator: false) 確保只操作當前 navigator
        if (autoCloseSheet &&
            Navigator.of(context, rootNavigator: false).canPop()) {
          Navigator.of(context, rootNavigator: false).pop();
        }
        onTap();
      },
    );
  }

  /// 編輯貼文
  void _editPost(BuildContext context) {
    debugPrint('📝 PostCard: 編輯貼文 - ${widget.post.postId}');
    context.push('/edit-post/${widget.post.postId}');
  }

  /// 刪除貼文
  void _deletePost(BuildContext context) async {
    debugPrint('🗑️ PostCard: 刪除貼文 - ${widget.post.postId}');

    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => DeleteConfirmationDialog(
        title: '刪除貼文',
        message: '確定要刪除這篇貼文嗎？此操作無法復原。',
        onConfirm: () => Navigator.of(context, rootNavigator: false).pop(true),
      ),
    );

    if (confirmed == true && mounted) {
      try {
        await ref
            .read(crudProvider.notifier)
            .deletePost(postId: widget.post.postId);
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('貼文已刪除')),
          );
          // 呼叫刪除後回調以刷新列表
          widget.onPostDeleted?.call();
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('刪除失敗：$e')),
          );
        }
      }
    }
  }

  /// 檢舉貼文
  void _reportPost(BuildContext context) async {
    debugPrint('🚨 PostCard: 檢舉貼文 - ${widget.post.postId}');

    final success = await AuthGuard.requireLogin(
      context: context,
      ref: ref,
      title: '檢舉貼文',
      message: '登入後即可檢舉不當內容',
      onLoginSuccess: () => _showReportDialog(context),
    );

    if (success && mounted) {
      _showReportDialog(context);
    }
  }

  /// 顯示檢舉對話框
  void _showReportDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => ReportDialog(
        targetType: ReportTargetType.post,
        targetId: widget.post.postId,
      ),
    );
  }

  /// 管理員操作
  void _showAdminOptions(BuildContext context) {
    debugPrint('👮 PostCard: 管理員操作 - ${widget.post.postId}');
    // TODO: 實作管理員操作面板
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('管理員功能開發中')),
    );
  }

  /// 封鎖用戶
  void _blockUser(BuildContext context) async {
    debugPrint('🚫 PostCard: 封鎖用戶 - ${widget.post.author.userId}');

    final success = await AuthGuard.requireLogin(
      context: context,
      ref: ref,
      title: '封鎖用戶',
      message: '登入後即可封鎖用戶',
      onLoginSuccess: () => _showBlockConfirmDialog(context),
    );

    if (success && mounted) {
      _showBlockConfirmDialog(context);
    }
  }

  /// 顯示封鎖確認對話框
  void _showBlockConfirmDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('確認封鎖'),
        content: Text(
            '確定要封鎖 ${widget.post.author.displayName} 嗎？封鎖後將不會看到該用戶的貼文和評論。'),
        actions: [
          TextButton(
            onPressed: () {
              if (Navigator.of(context, rootNavigator: false).canPop()) {
                Navigator.of(context, rootNavigator: false).pop();
              }
            },
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              if (Navigator.of(context, rootNavigator: false).canPop()) {
                Navigator.of(context, rootNavigator: false).pop();
              }
              _performBlockUser();
            },
            style: TextButton.styleFrom(
              foregroundColor: Theme.of(context).colorScheme.error,
            ),
            child: const Text('封鎖'),
          ),
        ],
      ),
    );
  }

  /// 執行封鎖用戶操作
  void _performBlockUser() async {
    try {
      // 確定用戶識別符：匿名用戶使用 userHash，一般用戶使用 userId
      final identifier = widget.post.author.isAnonymous
          ? widget.post.author.userHash ?? widget.post.author.userId
          : widget.post.author.userId;

      // 執行封鎖操作
      await ref.read(blockedUsersProvider.notifier).blockUser(
            targetUid: identifier,
            displayName: widget.post.author.displayName,
            avatarUrl: widget.post.author.photoURL,
          );

      debugPrint(
          '🚫 PostCard: 已封鎖用戶 - $identifier (匿名: ${widget.post.author.isAnonymous})');

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('已封鎖 ${widget.post.author.displayName}')),
        );
      }
    } catch (e) {
      debugPrint('❌ PostCard: 封鎖失敗 - $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('封鎖失敗：$e')),
        );
      }
    }
  }

  /// 執行追蹤用戶操作（不涉及導航）
  Future<void> _performFollowUser() async {
    try {
      // 🔥 對於匿名用戶，使用 userHash；對於一般用戶，使用 userId
      final targetId = widget.post.author.isAnonymous
          ? (widget.post.author.userHash ?? widget.post.author.userId)
          : widget.post.author.userId;

      await ref.read(postInteractionProvider.notifier).followUser(targetId);

      debugPrint(
          '✅ PostCard: 已追蹤用戶 - $targetId (匿名: ${widget.post.author.isAnonymous})');

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('已追蹤 ${widget.post.author.effectiveDisplayName}'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      debugPrint('❌ PostCard: 追蹤失敗 - $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('追蹤失敗：$e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// 執行取消追蹤用戶操作（不涉及導航）
  Future<void> _performUnfollowUser() async {
    try {
      // 🔥 對於匿名用戶，使用 userHash；對於一般用戶，使用 userId
      final targetId = widget.post.author.isAnonymous
          ? (widget.post.author.userHash ?? widget.post.author.userId)
          : widget.post.author.userId;

      await ref.read(postInteractionProvider.notifier).unfollowUser(targetId);

      debugPrint(
          '✅ PostCard: 已取消追蹤用戶 - $targetId (匿名: ${widget.post.author.isAnonymous})');

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('已取消追蹤 ${widget.post.author.effectiveDisplayName}'),
            backgroundColor: Colors.orange,
          ),
        );
      }
    } catch (e) {
      debugPrint('❌ PostCard: 取消追蹤失敗 - $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('取消追蹤失敗：$e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../../../core/utils/logger.dart';
import '../../../notifications/data/services/notification_trigger_service.dart';

/// 用戶追蹤服務 - 處理用戶間的追蹤關係
class UserFollowService {
  static final UserFollowService _instance = UserFollowService._internal();
  factory UserFollowService() => _instance;
  UserFollowService._internal();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  SharedPreferences? _prefs;

  // 快取
  final Map<String, Set<String>> _followingCache = {};
  final Map<String, Set<String>> _followersCache = {};

  static Future<UserFollowService> createWithCache() async {
    final instance = UserFollowService();
    instance._prefs = await SharedPreferences.getInstance();
    return instance;
  }

  /// 初始化用戶快取
  Future<void> initializeUserCache(String userId) async {
    try {
      // 載入追蹤列表
      final followingSnapshot = await _firestore
          .collection('following')
          .doc(userId)
          .collection('user_following')
          .get();

      _followingCache[userId] =
          followingSnapshot.docs.map((doc) => doc.id).toSet();

      // 載入粉絲列表
      final followersSnapshot = await _firestore
          .collection('followers')
          .doc(userId)
          .collection('user_followers')
          .get();

      _followersCache[userId] =
          followersSnapshot.docs.map((doc) => doc.id).toSet();

      Logger.i('✅ User follow cache initialized for $userId');
    } catch (e) {
      Logger.e('❌ Failed to initialize user follow cache: $e');
    }
  }

  /// 追蹤用戶
  Future<void> followUser(String currentUserId, [String? targetUserId]) async {
    if (targetUserId == null || currentUserId == targetUserId) return;

    try {
      // 更新本地快取
      _followingCache[currentUserId] ??= {};
      _followingCache[currentUserId]!.add(targetUserId);

      // 使用批次寫入
      final batch = _firestore.batch();

      // 1. 添加到當前用戶的 following 列表
      final followingRef = _firestore
          .collection('following')
          .doc(currentUserId)
          .collection('user_following')
          .doc(targetUserId);

      batch.set(followingRef, {
        'followed_at': FieldValue.serverTimestamp(),
        'target_user_id': targetUserId,
      });

      // 2. 添加到目標用戶的 followers 列表
      final followersRef = _firestore
          .collection('followers')
          .doc(targetUserId)
          .collection('user_followers')
          .doc(currentUserId);

      batch.set(followersRef, {
        'followed_at': FieldValue.serverTimestamp(),
        'follower_id': currentUserId,
      });

      // 3. 更新用戶統計
      final currentUserRef = _firestore.collection('users').doc(currentUserId);
      batch.update(currentUserRef, {
        'stats.followingCount': FieldValue.increment(1),
      });

      final targetUserRef = _firestore.collection('users').doc(targetUserId);
      batch.update(targetUserRef, {
        'stats.followersCount': FieldValue.increment(1),
      });

      // 提交批次操作
      await batch.commit();

      // 發送追蹤通知
      try {
        await NotificationTriggerService.sendFollowNotification(
          followerId: currentUserId,
          targetUserId: targetUserId,
        );
      } catch (notificationError) {
        Logger.w('⚠️ Failed to send follow notification: $notificationError');
        // 不要因為通知失敗而影響追蹤功能
      }

      // 活動記錄由通知系統處理，避免重複記錄

      Logger.i('✅ Successfully followed user: $targetUserId');
    } catch (e) {
      // 還原本地快取
      _followingCache[currentUserId]?.remove(targetUserId);
      Logger.e('❌ Failed to follow user: $e');
      rethrow;
    }
  }

  /// 取消追蹤用戶
  Future<void> unfollowUser(String currentUserId,
      [String? targetUserId]) async {
    if (targetUserId == null || currentUserId == targetUserId) return;

    try {
      // 更新本地快取
      _followingCache[currentUserId]?.remove(targetUserId);

      // 使用批次寫入
      final batch = _firestore.batch();

      // 1. 從當前用戶的 following 列表刪除
      final followingRef = _firestore
          .collection('following')
          .doc(currentUserId)
          .collection('user_following')
          .doc(targetUserId);

      batch.delete(followingRef);

      // 2. 從目標用戶的 followers 列表刪除
      final followersRef = _firestore
          .collection('followers')
          .doc(targetUserId)
          .collection('user_followers')
          .doc(currentUserId);

      batch.delete(followersRef);

      // 3. 更新用戶統計
      final currentUserRef = _firestore.collection('users').doc(currentUserId);
      batch.update(currentUserRef, {
        'stats.followingCount': FieldValue.increment(-1),
      });

      final targetUserRef = _firestore.collection('users').doc(targetUserId);
      batch.update(targetUserRef, {
        'stats.followersCount': FieldValue.increment(-1),
      });

      // 提交批次操作
      await batch.commit();

      Logger.i('✅ Successfully unfollowed user: $targetUserId');
    } catch (e) {
      // 還原本地快取
      _followingCache[currentUserId] ??= {};
      _followingCache[currentUserId]!.add(targetUserId);
      Logger.e('❌ Failed to unfollow user: $e');
      rethrow;
    }
  }

  /// 檢查是否正在追蹤某用戶
  Future<bool> isFollowing(String currentUserId, [String? targetUserId]) async {
    if (targetUserId == null || currentUserId == targetUserId) return false;

    // 先檢查快取
    if (_followingCache.containsKey(currentUserId)) {
      return _followingCache[currentUserId]!.contains(targetUserId);
    }

    // 如果快取不存在，查詢資料庫
    try {
      final doc = await _firestore
          .collection('following')
          .doc(currentUserId)
          .collection('user_following')
          .doc(targetUserId)
          .get();

      return doc.exists;
    } catch (e) {
      Logger.e('❌ Failed to check following status: $e');
      return false;
    }
  }

  /// 獲取追蹤列表
  Future<List<String>> getFollowing([String? userId, int limit = 20]) async {
    if (userId == null) return [];

    try {
      final snapshot = await _firestore
          .collection('following')
          .doc(userId)
          .collection('user_following')
          .orderBy('followed_at', descending: true)
          .limit(limit)
          .get();

      return snapshot.docs.map((doc) => doc.id).toList();
    } catch (e) {
      Logger.e('❌ Failed to get following list: $e');
      return [];
    }
  }

  /// 獲取粉絲列表
  Future<List<String>> getFollowers([String? userId, int limit = 20]) async {
    if (userId == null) return [];

    try {
      final snapshot = await _firestore
          .collection('followers')
          .doc(userId)
          .collection('user_followers')
          .orderBy('followed_at', descending: true)
          .limit(limit)
          .get();

      return snapshot.docs.map((doc) => doc.id).toList();
    } catch (e) {
      Logger.e('❌ Failed to get followers list: $e');
      return [];
    }
  }

  /// 獲取追蹤統計
  Future<Map<String, int>> getFollowStats(String userId) async {
    try {
      final userDoc = await _firestore.collection('users').doc(userId).get();

      if (userDoc.exists) {
        final data = userDoc.data() as Map<String, dynamic>?;
        return {
          'following': data?['stats']?['followingCount'] ?? 0,
          'followers': data?['stats']?['followersCount'] ?? 0,
        };
      }

      return {'following': 0, 'followers': 0};
    } catch (e) {
      Logger.e('❌ Failed to get follow stats: $e');
      return {'following': 0, 'followers': 0};
    }
  }

  /// 清除用戶快取
  Future<void> clearUserCache(String userId) async {
    _followingCache.remove(userId);
    _followersCache.remove(userId);
  }

  /// 清除所有快取
  Future<void> clearAllCache() async {
    _followingCache.clear();
    _followersCache.clear();
  }
}

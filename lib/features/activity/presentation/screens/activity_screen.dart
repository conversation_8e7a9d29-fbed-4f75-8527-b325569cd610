// lib/features/activity/presentation/screens/activity_screen.dart

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:go_router/go_router.dart';

import '../../../../shared/animations/animation_manager_mixin.dart';
import '../../../../shared/widgets/app_widgets.dart';
import '../../../../core/theme/app_theme_config.dart';
import '../../../../core/auth/auth_guard.dart';
import '../../../auth/presentation/providers/auth_provider.dart';
import '../providers/activity_provider.dart';
import '../widgets/enhanced_activity_item.dart';
import '../widgets/components/enhanced_stats_card.dart';
import '../widgets/activity_guide_dialog.dart';
import '../widgets/enhanced_tab_bar.dart';
import '../../domain/entities/activity_record.dart';

/// 🎯 活動動態頁面
class ActivityScreen extends ConsumerStatefulWidget {
  const ActivityScreen({super.key});

  @override
  ConsumerState<ActivityScreen> createState() => _ActivityScreenState();
}

class _ActivityScreenState extends ConsumerState<ActivityScreen>
    with TickerProviderStateMixin, AnimationLifecycleMixin {
  late TabController _tabController;
  late ScrollController _scrollController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _scrollController = ScrollController();

    // 設定動畫
    setupEntranceAnimations();

    // 監聽滾動以實現無限載入
    _scrollController.addListener(_onScroll);

    // 初始載入數據
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadInitialData();
      _showGuideIfNeeded();
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  /// 載入初始數據
  void _loadInitialData() {
    final authState = ref.read(authProvider);
    if (authState.hasValue && authState.value != null) {
      ref
          .read(activityProvider.notifier)
          .loadUserActivities(authState.value!.uid, refresh: true);
    }
  }

  /// 顯示首次使用指引
  void _showGuideIfNeeded() async {
    // 延遲一點時間，確保頁面已經完全載入
    await Future.delayed(const Duration(milliseconds: 1000));
    if (mounted) {
      await ActivityGuideDialog.showIfNeeded(context);
    }
  }

  /// 滾動監聽
  void _onScroll() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 200) {
      final authState = ref.read(authProvider);
      if (authState.hasValue && authState.value != null) {
        ref
            .read(activityProvider.notifier)
            .loadMoreActivities(authState.value!.uid);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final authState = ref.watch(authProvider);

    // 如果用戶未登入，顯示登入提示
    if (!authState.hasValue || authState.value == null) {
      return _buildLoginPrompt(context);
    }

    return Scaffold(
      appBar: AppBar(
        title: AppText.title('activity.title'.tr()),
        automaticallyImplyLeading: false,
        elevation: 0,
        backgroundColor: Colors.transparent,
        actions: [
          // 標記全部已讀按鈕
          Consumer(
            builder: (context, ref, child) {
              final unreadCount = ref.watch(unreadActivityCountProvider);
              if (unreadCount > 0) {
                return IconButton(
                  icon: const Icon(Icons.done_all),
                  onPressed: () => _markAllAsRead(),
                  tooltip: 'activity.mark_all_read'.tr(),
                );
              }
              return const SizedBox.shrink();
            },
          ),
        ],
        bottom: PreferredSize(
          preferredSize: const Size.fromHeight(56),
          child: EnhancedActivityTabBar(
            controller: _tabController,
            showAnimation: true,
          ),
        ),
      ),
      body: SafeArea(
        child: TabBarView(
          controller: _tabController,
          children: [
            _TodayActivitiesTab(scrollController: _scrollController),
            _WeekHighlightsTab(scrollController: _scrollController),
            _AllActivitiesTab(scrollController: _scrollController),
          ],
        ),
      ),
    );
  }

  /// 建立登入提示
  Widget _buildLoginPrompt(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: AppText.title('activity.title'.tr()),
        automaticallyImplyLeading: false,
        elevation: 0,
        backgroundColor: Colors.transparent,
      ),
      body: Center(
        child: Padding(
          padding: EdgeInsets.all(AppThemeConfig.design.spacingLarge),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.login,
                size: 64,
                color: theme.colorScheme.outline,
              ),
              SizedBox(height: AppThemeConfig.design.spacingMedium),
              AppText.title('activity.login_required_title'.tr()),
              SizedBox(height: AppThemeConfig.design.spacingSmall),
              AppText.body(
                'activity.login_required_subtitle'.tr(),
                style: TextStyle(
                  color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                ),
                textAlign: TextAlign.center,
              ),
              SizedBox(height: AppThemeConfig.design.spacingLarge),
              AppButton.primary(
                text: 'auth.login'.tr(),
                onPressed: () async {
                  final success = await AuthGuard.requireLogin(
                    context: context,
                    ref: ref,
                    title: 'activity.login_required_title'.tr(),
                    message: 'activity.login_required_subtitle'.tr(),
                    onLoginSuccess: () {
                      // 登入成功後重新載入數據
                      _loadInitialData();
                    },
                  );

                  if (success) {
                    // 登入成功，重新載入數據
                    _loadInitialData();
                  }
                },
                icon: Icons.login,
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 標記所有活動為已讀
  void _markAllAsRead() {
    final authState = ref.read(authProvider);
    if (authState.hasValue && authState.value != null) {
      ref.read(activityProvider.notifier).markAllAsRead(authState.value!.uid);
    }
  }
}

/// 今日動態 Tab
class _TodayActivitiesTab extends ConsumerWidget {
  final ScrollController scrollController;

  const _TodayActivitiesTab({required this.scrollController});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final activityState = ref.watch(activityProvider);
    final todayActivities = activityState.todayActivities;

    return AppRefreshIndicator(
      onRefresh: () async {
        final authState = ref.read(authProvider);
        if (authState.hasValue && authState.value != null) {
          await ref
              .read(activityProvider.notifier)
              .refreshActivities(authState.value!.uid);
        }
      },
      child: CustomScrollView(
        controller: scrollController,
        slivers: [
          // 增強版統計卡片
          SliverToBoxAdapter(
            child: Padding(
              padding: EdgeInsets.all(AppThemeConfig.design.spacingMedium),
              child: EnhancedStatsCard(
                stats: activityState.stats,
                onStatTap: (statType) {
                  // TODO: 處理統計項目點擊，篩選相應內容
                },
                onTimeRangeChanged: (timeRange) {
                  // TODO: 處理時間範圍變更
                },
              ),
            ),
          ),

          // 今日活動列表
          if (todayActivities.isEmpty && !activityState.isLoading)
            SliverToBoxAdapter(
              child: Padding(
                padding: EdgeInsets.all(AppThemeConfig.design.spacingMedium),
                child: EntranceAnimation(
                  delay: const Duration(milliseconds: 300),
                  child: AppEmptyState(
                    icon: Icons.today_outlined,
                    title: 'activity.no_today_activities_title'.tr(),
                    message: 'activity.no_today_activities_subtitle'.tr(),
                  ),
                ),
              ),
            )
          else
            SliverList(
              delegate: SliverChildBuilderDelegate(
                (context, index) {
                  if (index < todayActivities.length) {
                    return EnhancedActivityItem(
                      activity: todayActivities[index],
                      animationIndex: index,
                      showAnimation: true,
                      onTap: () => _handleActivityTap(
                          context, ref, todayActivities[index]),
                      onMarkAsRead: () =>
                          _markAsRead(ref, todayActivities[index].activityId),
                    );
                  }
                  return null;
                },
                childCount: todayActivities.length,
              ),
            ),

          // 載入指示器
          if (activityState.isLoading)
            const SliverToBoxAdapter(
              child: Padding(
                padding: EdgeInsets.all(16.0),
                child: Center(child: AppLoadingIndicator()),
              ),
            ),
        ],
      ),
    );
  }

  void _handleActivityTap(
      BuildContext context, WidgetRef ref, ActivityRecord activity) {
    // 根據活動類型導航到相關頁面
    switch (activity.type) {
      case ActivityType.receivedLike:
      case ActivityType.receivedComment:
      case ActivityType.postBookmarked:
      case ActivityType.followedUserPosted:
        // 導航到貼文詳情頁
        if (activity.postId != null) {
          context.push('/post/${activity.postId}');
        }
        break;

      case ActivityType.receivedFollow:
        // 導航到用戶資料頁
        if (activity.triggerUserId != null) {
          context.push('/profile/${activity.triggerUserId}');
        }
        break;

      case ActivityType.reputationGained:
        // 可以導航到聲望詳情頁或相關貼文
        if (activity.postId != null) {
          context.push('/post/${activity.postId}');
        }
        break;

      default:
        // 其他類型的活動暫時不處理
        break;
    }

    // 標記為已讀
    _markAsRead(ref, activity.activityId);
  }

  void _markAsRead(WidgetRef ref, String activityId) {
    ref.read(activityProvider.notifier).markAsRead(activityId);
  }
}

/// 本週精華 Tab
class _WeekHighlightsTab extends ConsumerWidget {
  final ScrollController scrollController;

  const _WeekHighlightsTab({required this.scrollController});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final activityState = ref.watch(activityProvider);
    final weekHighlights = activityState.weekHighlights;

    return AppRefreshIndicator(
      onRefresh: () async {
        final authState = ref.read(authProvider);
        if (authState.hasValue && authState.value != null) {
          await ref
              .read(activityProvider.notifier)
              .refreshActivities(authState.value!.uid);
        }
      },
      child: CustomScrollView(
        controller: scrollController,
        slivers: [
          if (weekHighlights.isEmpty && !activityState.isLoading)
            SliverToBoxAdapter(
              child: Padding(
                padding: EdgeInsets.all(AppThemeConfig.design.spacingMedium),
                child: EntranceAnimation(
                  delay: const Duration(milliseconds: 300),
                  child: AppEmptyState(
                    icon: Icons.star_outline,
                    title: 'activity.no_highlights_title'.tr(),
                    message: 'activity.no_highlights_subtitle'.tr(),
                  ),
                ),
              ),
            )
          else
            SliverList(
              delegate: SliverChildBuilderDelegate(
                (context, index) {
                  if (index < weekHighlights.length) {
                    return EnhancedActivityItem(
                      activity: weekHighlights[index],
                      isHighlight: true,
                      animationIndex: index,
                      showAnimation: true,
                      onTap: () => _handleActivityTap(
                          context, ref, weekHighlights[index]),
                      onMarkAsRead: () =>
                          _markAsRead(ref, weekHighlights[index].activityId),
                    );
                  }
                  return null;
                },
                childCount: weekHighlights.length,
              ),
            ),
          if (activityState.isLoading)
            const SliverToBoxAdapter(
              child: Padding(
                padding: EdgeInsets.all(16.0),
                child: Center(child: AppLoadingIndicator()),
              ),
            ),
        ],
      ),
    );
  }

  void _handleActivityTap(
      BuildContext context, WidgetRef ref, ActivityRecord activity) {
    // 根據活動類型導航到相關頁面
    switch (activity.type) {
      case ActivityType.receivedLike:
      case ActivityType.receivedComment:
      case ActivityType.postBookmarked:
      case ActivityType.followedUserPosted:
        // 導航到貼文詳情頁
        if (activity.postId != null) {
          context.push('/post/${activity.postId}');
        }
        break;

      case ActivityType.receivedFollow:
        // 導航到用戶資料頁
        if (activity.triggerUserId != null) {
          context.push('/profile/${activity.triggerUserId}');
        }
        break;

      case ActivityType.reputationGained:
        // 可以導航到聲望詳情頁或相關貼文
        if (activity.postId != null) {
          context.push('/post/${activity.postId}');
        }
        break;

      default:
        // 其他類型的活動暫時不處理
        break;
    }

    // 標記為已讀
    _markAsRead(ref, activity.activityId);
  }

  void _markAsRead(WidgetRef ref, String activityId) {
    ref.read(activityProvider.notifier).markAsRead(activityId);
  }
}

/// 全部活動 Tab
class _AllActivitiesTab extends ConsumerWidget {
  final ScrollController scrollController;

  const _AllActivitiesTab({required this.scrollController});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final activityState = ref.watch(activityProvider);
    final allActivities = activityState.allActivities;

    return AppRefreshIndicator(
      onRefresh: () async {
        final authState = ref.read(authProvider);
        if (authState.hasValue && authState.value != null) {
          await ref
              .read(activityProvider.notifier)
              .refreshActivities(authState.value!.uid);
        }
      },
      child: CustomScrollView(
        controller: scrollController,
        slivers: [
          if (allActivities.isEmpty && !activityState.isLoading)
            SliverToBoxAdapter(
              child: Padding(
                padding: EdgeInsets.all(AppThemeConfig.design.spacingMedium),
                child: EntranceAnimation(
                  delay: const Duration(milliseconds: 300),
                  child: AppEmptyState(
                    icon: Icons.history,
                    title: 'activity.no_activities_title'.tr(),
                    message: 'activity.no_activities_subtitle'.tr(),
                  ),
                ),
              ),
            )
          else
            SliverList(
              delegate: SliverChildBuilderDelegate(
                (context, index) {
                  if (index < allActivities.length) {
                    return EnhancedActivityItem(
                      activity: allActivities[index],
                      animationIndex: index,
                      showAnimation: true,
                      onTap: () => _handleActivityTap(
                          context, ref, allActivities[index]),
                      onMarkAsRead: () =>
                          _markAsRead(ref, allActivities[index].activityId),
                    );
                  } else if (activityState.isLoadingMore) {
                    return const Padding(
                      padding: EdgeInsets.all(16.0),
                      child: Center(child: AppLoadingIndicator()),
                    );
                  }
                  return null;
                },
                childCount: allActivities.length +
                    (activityState.isLoadingMore ? 1 : 0),
              ),
            ),
          if (activityState.isLoading)
            const SliverToBoxAdapter(
              child: Padding(
                padding: EdgeInsets.all(16.0),
                child: Center(child: AppLoadingIndicator()),
              ),
            ),
        ],
      ),
    );
  }

  void _handleActivityTap(
      BuildContext context, WidgetRef ref, ActivityRecord activity) {
    // 根據活動類型導航到相關頁面
    switch (activity.type) {
      case ActivityType.receivedLike:
      case ActivityType.receivedComment:
      case ActivityType.postBookmarked:
      case ActivityType.followedUserPosted:
        // 導航到貼文詳情頁
        if (activity.postId != null) {
          context.push('/post/${activity.postId}');
        }
        break;

      case ActivityType.receivedFollow:
        // 導航到用戶資料頁
        if (activity.triggerUserId != null) {
          context.push('/profile/${activity.triggerUserId}');
        }
        break;

      case ActivityType.reputationGained:
        // 可以導航到聲望詳情頁或相關貼文
        if (activity.postId != null) {
          context.push('/post/${activity.postId}');
        }
        break;

      default:
        // 其他類型的活動暫時不處理
        break;
    }

    // 標記為已讀
    _markAsRead(ref, activity.activityId);
  }

  void _markAsRead(WidgetRef ref, String activityId) {
    ref.read(activityProvider.notifier).markAsRead(activityId);
  }
}
